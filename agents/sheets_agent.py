from typing import Annotated, TypedDict, List, Dict, Any, Optional
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langgraph.prebuilt import ToolNode
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from langchain_openai import ChatOpenAI
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class AgentState(TypedDict):
    """State for the ReAct agent"""
    messages: Annotated[List[BaseMessage], add_messages]

class SheetsReActAgent:
    """A ReAct agent for Google Sheets operations using LangGraph"""

    def __init__(self):
        # Initialize the LLM
        self.llm = ChatOpenAI(
            model="gpt-4o-mini",
            temperature=0,
            api_key=os.getenv("OPENAI_API_KEY")
        )

        # Initialize tools (you'll implement these)
        self.tools = self._get_tools()

        # Create the graph
        self.graph = self._create_graph()

    def _get_tools(self):
        """Define the tools for the agent. You'll implement these."""
        # TODO: Implement your Google Sheets tools here
        # Example tools might include:
        # - list_sheets_tool
        # - read_sheet_tool
        # - write_sheet_tool
        # - create_sheet_tool
        return []

    def _create_graph(self):
        """Create the LangGraph workflow"""
        # Create the graph
        workflow = StateGraph(AgentState)

        # Add nodes
        workflow.add_node("agent", self._agent_node)

        # Add tools node if we have tools
        if self.tools:
            tool_node = ToolNode(self.tools)
            workflow.add_node("tools", tool_node)

        # Add edges
        workflow.add_edge(START, "agent")

        if self.tools:
            # Conditional edge from agent to tools or end
            workflow.add_conditional_edges(
                "agent",
                self._should_continue,
                {
                    "continue": "tools",
                    "end": END
                }
            )
            # Edge from tools back to agent
            workflow.add_edge("tools", "agent")
        else:
            # If no tools, go directly to end
            workflow.add_edge("agent", END)

        # Compile the graph
        return workflow.compile()

    def _agent_node(self, state: AgentState):
        """The main agent node that processes messages and decides on actions"""
        messages = state["messages"]

        if self.tools:
            # Bind tools to the LLM for tool calling
            llm_with_tools = self.llm.bind_tools(self.tools)
            response = llm_with_tools.invoke(messages)
        else:
            # No tools available, just respond
            response = self.llm.invoke(messages)

        return {"messages": [response]}

    def _should_continue(self, state: AgentState):
        """Determine whether to continue to tools or end"""
        messages = state["messages"]
        last_message = messages[-1]

        # If the last message has tool calls, continue to tools
        if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
            return "continue"
        else:
            return "end"

    def run(self, user_input: str) -> str:
        """Run the agent with user input"""
        # Create initial state
        initial_state = {
            "messages": [HumanMessage(content=user_input)]
        }

        # Run the graph
        result = self.graph.invoke(initial_state)

        # Return the last AI message
        last_message = result["messages"][-1]
        return last_message.content if hasattr(last_message, 'content') else str(last_message)

# Example usage
if __name__ == "__main__":
    agent = SheetsReActAgent()

    # Test the agent
    response = agent.run("Hello! Can you help me with Google Sheets?")
    print(response)