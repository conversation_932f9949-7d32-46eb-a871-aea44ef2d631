from fastapi import <PERSON><PERSON><PERSON>, Request, Query
from fastapi.responses import RedirectResponse, JSONResponse
from sheets.sheets_app import SheetsApp

app = FastAPI()
sheets_app = SheetsApp()

@app.get("/auth/google")
def auth_google():
    flow = sheets_app.get_flow()
    auth_url, _ = flow.authorization_url(
        access_type="offline", include_granted_scopes="true"
    )
    return RedirectResponse(auth_url)

@app.get("/auth/callback")
def auth_callback(request: Request):
    code = request.query_params.get("code")
    if not code:
        return JSONResponse({"error": "No code provided"}, status_code=400)

    flow = sheets_app.get_flow()
    flow.fetch_token(code=code)
    creds = flow.credentials
    sheets_app.save_credentials(creds)

    return JSONResponse({"message": "Authentication successful!"})

@app.get("/sheets")
def list_google_sheets():
    return {"sheets": sheets_app.list_sheets()}

@app.get("/sheets/{spreadsheet_id}")
def get_sheet_details(spreadsheet_id: str):
    details = sheets_app.get_sheet_details(spreadsheet_id)
    return details

@app.get("/sheets/{spreadsheet_id}/values/{sheet_id}")
def get_sheet_values(spreadsheet_id: str, sheet_id: int):
    values = sheets_app.get_sheet_values(spreadsheet_id, sheet_id)
    return {"values": values}
